<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * This method is essential for the middleware API to handle scheduled tasks such as:
     * - Token refresh operations for SSO authentication
     * - Cache cleanup for authentication sessions
     * - Health checks for external service connections (Okta, Una)
     * - Maintenance tasks for the React Native app's backend operations
     */
    protected function schedule(Schedule $schedule): void
    {
        Log::info('Console Kernel: Initializing scheduled tasks for middleware API operations');

        // Future scheduled tasks will be added here for:
        // - SSO token management
        // - Cache maintenance
        // - External service health monitoring

        // $schedule->command('inspire')->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * This method is crucial for the middleware API as it:
     * 1. Loads custom Artisan commands from the Commands directory
     * 2. Registers console routes for CLI operations
     * 3. Enables command-line management of authentication flows
     * 4. Provides debugging capabilities for SSO integration
     */
    protected function commands(): void
    {
        Log::info('Console Kernel: Registering Artisan commands for middleware API management');

        // Load all custom commands from the Commands directory
        // This enables CLI tools for managing SSO authentication, debugging auth flows,
        // and maintaining the middleware's connection to external services
        $this->load(__DIR__.'/Commands');

        // Include console routes for additional CLI functionality
        // These routes support development and production maintenance tasks
        require base_path('routes/console.php');

        Log::info('Console Kernel: Successfully registered all commands and console routes');
    }
}
