<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
class AuthController extends Controller
{
    private OktaService $oktaService;

    public function __construct(OktaService $oktaService)
    {
        $this->oktaService = $oktaService;
    }
    ///Unsafe dev only purposes thing.
    public function devLogin(Request $request)
    {
        $email = $request->input('email');

        $user = \App\Models\User::where('email', $email)->first();

        if (!$user) {
            $user = $this->createUser($email);
        }
        if ($user != null) {
            auth()->login($user);
            $sessionId = $user->createToken('api_token')->plainTextToken;

            // Best-effort: persist token hash on latest active session if present
            try {
                $session = UserSession::where('user_id', $user->id)
                    ->where('is_active', true)
                    ->latest()
                    ->first();
                if ($session) {
                    $session->app_token_hash = hash('sha256', $sessionId);
                    $session->save();
                }
            } catch (\Exception $e) {
                Log::warning('devLogin failed to persist app_token_hash', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }

            return response()->json([
                'message' => 'Logged in as user',
                'session' => $sessionId
            ]);
        } else {
            return response()->json(['error' => 'Unable to login or create user'], 500);
        }

    }

    public function logout(Request $request)
    {
        $user = $request->user();

        // Get the current token to find the associated session
        $currentToken = $request->bearerToken();
        $tokenHash = hash('sha256', $currentToken);

        // Find and deactivate the user session
        $userSession = UserSession::where('user_id', $user->id)
            ->where('app_token_hash', $tokenHash)
            ->where('is_active', true)
            ->first();

        if ($userSession) {
            // Revoke Okta tokens
            if ($userSession->okta_access_token) {
                try {
                    $accessToken = decrypt($userSession->okta_access_token);
                    $this->oktaService->revokeToken($accessToken, 'access_token');
                } catch (\Exception $e) {
                    // Token might not be encrypted in tests
                    $this->oktaService->revokeToken($userSession->okta_access_token, 'access_token');
                }
            }
            if ($userSession->okta_refresh_token) {
                try {
                    $refreshToken = decrypt($userSession->okta_refresh_token);
                    $this->oktaService->revokeToken($refreshToken, 'refresh_token');
                } catch (\Exception $e) {
                    // Token might not be encrypted in tests
                    $this->oktaService->revokeToken($userSession->okta_refresh_token, 'refresh_token');
                }
            }

            // Deactivate the session
            $userSession->deactivate();
        }

        // Delete Sanctum tokens
        $user->tokens()->delete();

        return response()->json(['message' => 'Logged out']);
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }

    /**
     * App-to-Middleware: Initiate Okta SSO login flow.
     * This is the endpoint that React Native apps call to start authentication.
     */
    public function login(Request $request): \Illuminate\Http\RedirectResponse
    {
        $platform = $request->get('platform', 'mobile');

        // Store platform in session for callback handling
        session(['auth_platform' => $platform]);

        // Generate state and PKCE challenge
        /*
        1. Middleware generates random state → Stores it (likely in session or database)
        2. Middleware redirects to Okta → Includes state in the authorization URL
        3. Okta redirects back to middleware → Returns the same state value
        4. Middleware validates → Confirms the returned state matches what was sent
        */
        $state = Str::random(40);
        $pkce = $this->oktaService->generatePkceChallenge();

        // Store state and PKCE in session for validation
        session([
            'oauth_state' => $state,
            'code_verifier' => $pkce['code_verifier'],
        ]);

        // Build authorization URL (this will redirect to Okta, which will callback to $APP_SCHEME://$APP_AUTH_CALLBACK_PATH)
        $authUrl = $this->oktaService->buildAuthorizationUrl($state, $pkce);

        Log::info('App-to-Middleware: Okta login initiated', [
            'platform' => $platform,
            'state' => $state,
        ]);

        return redirect($authUrl);
    }

    /**
     * Middleware-to-SSO: Handle Okta callback after user authentication.
     * This processes the authorization code and redirects to the app deep link.
     */
    public function callback(Request $request): \Illuminate\Http\RedirectResponse
    {
        Log::info('=== OKTA CALLBACK START ===', [
            'query_params' => $request->query(),
            'session_state' => session('oauth_state'),
            'session_verifier_present' => !empty(session('code_verifier')),
            'user_agent' => $request->header('User-Agent'),
        ]);

        // Validate state parameter
        if ($request->query('state') !== session('oauth_state')) {
            Log::error('Invalid state parameter', [
                'received' => $request->query('state'),
                'expected' => session('oauth_state')
            ]);
            return $this->handleCallbackError('Invalid state');
        }

        // Get authorization code
        $code = $request->query('code');
        if (!$code) {
            Log::error('Missing authorization code');
            return $this->handleCallbackError('Missing code');
        }

        Log::info('State validation passed', [
            'state' => $request->query('state'),
            'code_length' => strlen($code),
        ]);

        try {
            Log::info('=== TOKEN EXCHANGE START ===', [
                'grant_type' => 'authorization_code',
                'redirect_uri' => config('services.okta.redirect_uri'),
                'client_id' => config('services.okta.client_id'),
                'code_verifier_present' => !empty(session('code_verifier')),
                'code_length' => strlen($code),
            ]);

            // Exchange code for tokens using OktaService
            $tokenResponse = $this->oktaService->exchangeCodeForTokens($code, session('code_verifier'));

            Log::info('=== TOKEN EXCHANGE SUCCESS ===', [
                'access_token_present' => !empty($tokenResponse['access_token']),
                'id_token_present' => !empty($tokenResponse['id_token']),
                'refresh_token_present' => !empty($tokenResponse['refresh_token']),
                'expires_in' => $tokenResponse['expires_in'] ?? null,
                'token_type' => $tokenResponse['token_type'] ?? null,
            ]);

            // Fetch user profile using OktaService
            Log::info('=== USER INFO FETCH START ===', [
                'access_token_length' => strlen($tokenResponse['access_token']),
            ]);

            $userinfo = $this->oktaService->getUserProfile($tokenResponse['access_token']);

            Log::info('=== USER INFO FETCH SUCCESS ===', [
                'userinfo_keys' => array_keys($userinfo),
                'sub' => $userinfo['sub'] ?? 'missing',
                'email' => $userinfo['email'] ?? 'missing',
                'name' => $userinfo['name'] ?? 'missing',
                'preferred_username' => $userinfo['preferred_username'] ?? 'missing',
            ]);

            $user = [
                'sub' => $userinfo['sub'] ?? null,
                'email' => $userinfo['email'] ?? null,
                'name' => $userinfo['name'] ?? null,
            ];

            // Create or update user in database
            $dbUser = User::createOrUpdateFromOkta($userinfo);

            // Create user session with encrypted Okta tokens
            $userSession = $this->createUserSession($dbUser, $tokenResponse, $userinfo, $request);

            // Clear session data
            session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

            Log::info('=== JWT TOKEN CREATION START ===', [
                'user_data' => $user,
                'token_signing_key_present' => !empty(config('app.token_signing_key')),
            ]);

            // Create short-lived JWT token for deep link (60 seconds expiry)
            $shortLivedToken = $this->createShortLivedToken($user);

            Log::info('=== JWT TOKEN CREATED ===', [
                'token_length' => strlen($shortLivedToken),
                'token_preview' => substr($shortLivedToken, 0, 50) . '...',
            ]);

            // Build deep link URL
            $deepLinkUrl = $this->buildDeepLinkUrl($shortLivedToken, $user);

            Log::info('=== DEEP LINK REDIRECT ===', [
                'user_sub' => $user['sub'],
                'user_email' => $user['email'],
                'deep_link_url' => $deepLinkUrl,
                'deep_link_length' => strlen($deepLinkUrl),
            ]);

            // Redirect to deep link
            return redirect()->away($deepLinkUrl);

        } catch (\Exception $e) {
            Log::error('Okta callback failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return $this->handleCallbackError($e->getMessage());
        }
    }

    /**
     * App-to-Middleware: Display success page for web platform.
     * This shows the result after successful authentication.
     */
    public function success(Request $request)
    {
        $token = $request->get('token');
        $user = $request->get('user');

        // Return JSON instead of rendering a Blade view (mobile-first backend)
        return response()->json([
            'token' => $token,
            'user' => $user,
        ]);
    }

    /**
     * App-to-Middleware: Display error page for web platform.
     * This shows the result after failed authentication.
     */
    public function error(Request $request)
    {
        $error = $request->get('error', 'Authentication failed');

        // Return JSON error instead of rendering a Blade view
        return response()->json([
            'error' => $error,
        ], 400);
    }

    /**
     * App-to-Middleware: Exchange short-lived JWT for Laravel Sanctum API token.
     * This allows the app to convert the deep link JWT into a usable API token.
     */
    public function exchangeToken(Request $request): JsonResponse
    {
        Log::info('exchangeToken called', [
            'payload_keys' => array_keys($request->all()),
            'content_type' => $request->header('Content-Type'),
        ]);

        $jwtToken = $request->input('token');

        if (!$jwtToken) {
            Log::warning('exchangeToken missing token');
            return response()->json(['error' => 'Token is required'], 400);
        }

        try {
            // Decode and validate JWT
            $key = $this->getTokenSigningKey();
            $decoded = JWT::decode($jwtToken, new Key($key, 'HS256'));

            Log::info('JWT token exchange attempt', [
                'sub' => $decoded->sub ?? 'missing',
                'email' => $decoded->email ?? 'missing',
                'exp' => $decoded->exp ?? 'missing',
                'current_time' => time(),
            ]);

            // Check if token is expired (additional safety check)
            if (isset($decoded->exp) && $decoded->exp < time()) {
                Log::warning('Expired JWT token exchange attempt', [
                    'exp' => $decoded->exp,
                    'current_time' => time(),
                ]);
                return response()->json(['error' => 'Token expired'], 401);
            }

            // Find user by Okta sub
            $user = User::where('okta_user_id', $decoded->sub)->first();

            if (!$user) {
                Log::error('User not found for JWT token exchange', [
                    'okta_user_id' => $decoded->sub,
                ]);
                return response()->json(['error' => 'User not found'], 404);
            }

            // Create Sanctum token (same structure as devLogin)
            $sessionId = $user->createToken('api_token')->plainTextToken;

            // Persist app token hash to latest active session for reliable logout mapping
            try {
                $session = UserSession::where('user_id', $user->id)
                    ->where('is_active', true)
                    ->latest()
                    ->first();
                if ($session) {
                    $session->app_token_hash = hash('sha256', $sessionId);
                    $session->save();
                }
            } catch (\Exception $e) {
                Log::warning('Failed to persist app_token_hash', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
            }

            Log::info('JWT token exchange successful', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'token_length' => strlen($sessionId),
            ]);

            return response()->json([
                'message' => 'Token exchanged successfully',
                'session' => $sessionId  // Same structure as devLogin
            ]);

        } catch (\Firebase\JWT\ExpiredException $e) {
            Log::warning('Expired JWT token in exchange', [
                'error' => $e->getMessage(),
            ]);
            return response()->json(['error' => 'Token expired'], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            Log::error('Invalid JWT signature in exchange', [
                'error' => $e->getMessage(),
            ]);
            return response()->json(['error' => 'Invalid token signature'], 401);
        } catch (\Exception $e) {
            // Avoid logging raw token bytes which may be malformed UTF-8 and break JSON encoding
            $safePreview = base64_encode(substr($jwtToken, 0, 50));
            Log::error('JWT token exchange failed', [
                'error' => $e->getMessage(),
                'token_preview_b64' => $safePreview . '...',
            ]);
            return response()->json(['error' => 'Invalid token'], 401);
        }
    }

    /**
     * App-to-Middleware: Refresh app token if Okta session is still valid.
     * This allows apps to refresh their tokens without re-authentication.
     */
    public function refresh(Request $request): JsonResponse
    {
        $user = $request->user(); // From your app token

        // Check if Okta session is still valid
        if (!$this->isOktaSessionValid($user)) {
            return response()->json(['error' => 'Okta session expired'], 401);
        }

        // Generate new app token
        $newToken = $user->createToken('mobile-app')->plainTextToken;

        return response()->json(['token' => $newToken]);
    }

    /**
     * Create user session with encrypted Okta tokens.
     */
    private function createUserSession(User $user, array $tokens, array $oktaProfile, Request $request): UserSession
    {
        return UserSession::create([
            'user_id' => $user->id,
            // Pass raw tokens; Model mutators handle encryption at rest
            'okta_access_token' => $tokens['access_token'] ?? null,
            'okta_refresh_token' => $tokens['refresh_token'] ?? null,
            'okta_id_token' => $tokens['id_token'] ?? null,
            'okta_expires_at' => now()->addSeconds($tokens['expires_in'] ?? 3600),
            'platform' => session('auth_platform', 'mobile'),
            'okta_user_data' => $oktaProfile,
            'is_active' => true,
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Check if Okta session is still valid.
     */
    private function isOktaSessionValid(User $user): bool
    {
        $session = UserSession::where('user_id', $user->id)
            ->where('is_active', true)
            ->latest()
            ->first();

        if (!$session || !$session->isOktaSessionValid()) {
            return false;
        }

        // Update activity timestamp
        $session->updateActivity();

        return true;
    }

    private function createUser($email)
    {
        try {
            return \App\Models\User::create([
                'name' => $email, // Use email as name for dev login
                'email' => $email,
            ]);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Create a short-lived JWT token for deep link authentication.
     */
    private function createShortLivedToken(array $user): string
    {
        $now = time();
        $payload = [
            'sub' => $user['sub'],
            'email' => $user['email'],
            'name' => $user['name'],
            'exp' => $now + 60, // 60 seconds expiry
            'iat' => $now,
        ];

        Log::info('JWT payload created', [
            'payload' => $payload,
            'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
            'issued_at' => date('Y-m-d H:i:s', $payload['iat']),
        ]);

        $key = $this->getTokenSigningKey();
        $token = JWT::encode($payload, $key, 'HS256');

        Log::info('JWT token encoded', [
            'key_length' => strlen($key),
            'algorithm' => 'HS256',
        ]);

        return $token;
    }

    /**
     * Build the deep link URL with token and user data.
     */
    private function buildDeepLinkUrl(string $token, array $user): string
    {
        $userJson = urlencode(json_encode($user));
        $queryParams = 'token=' . urlencode($token) . '&user=' . $userJson;

        $scheme = config('app.scheme');
        $path = config('app.auth_callback_path');
        $baseUrl = $scheme . '://' . $path;

        return $baseUrl . '?' . $queryParams;
    }

    /**
     * Handle callback errors by redirecting to deep link with error.
     */
    private function handleCallbackError(string $error): \Illuminate\Http\RedirectResponse
    {
        // Clear session data
        session()->forget(['oauth_state', 'code_verifier', 'auth_platform']);

        $queryParams = 'error=' . urlencode($error);

        $scheme = config('app.scheme');
        $path = config('app.auth_callback_path');
        $baseUrl = $scheme . '://' . $path;
        $errorUrl = $baseUrl . '?' . $queryParams;

        Log::info('Redirecting to deep link with error', [
            'error' => $error,
            'error_url' => $errorUrl,
        ]);

        return redirect()->away($errorUrl);
    }

    /**
     * Resolve and validate the token signing key from configuration.
     * Requires a base64-encoded key of at least 32 bytes after decoding.
     */
    private function getTokenSigningKey(): string
    {
        $configured = (string) config('app.token_signing_key', '');
        if ($configured === '') {
            throw new \RuntimeException('Token signing key is not configured');
        }

        // Allow Laravel-style prefix "base64:" and raw base64 strings
        $normalized = str_starts_with($configured, 'base64:') ? substr($configured, 7) : $configured;

        $decoded = base64_decode($normalized, true);
        if ($decoded === false) {
            throw new \RuntimeException('Token signing key must be valid base64');
        }

        if (strlen($decoded) < 32) { // 256-bit minimum
            throw new \RuntimeException('Token signing key is too short; require >= 32 bytes');
        }

        return $decoded;
    }
}
