<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PostsController;


// DEV AUTHENTICATION (temporary)
// This endpoint is for local development only and must never be exposed in prod.
if (app()->environment('local')) {
    Route::post('/dev-login', [AuthController::class, 'devLogin'])->name('api.auth.dev-login');
}

// API endpoints consumed by the React Native app should be stateless and
// protected by token auth (Sanctum). Apply API throttling to mitigate abuse.
Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    // Secured API endpoints
    Route::post('/logout', [AuthController::class, 'logout'])->name('api.auth.logout');
    Route::get('/user', [AuthController::class, 'user'])->name('api.auth.user');
    Route::post('/refresh', [AuthController::class, 'refresh'])->name('api.auth.refresh');

    // POSTS (preferred RESTful, pluralized routes)
    // New canonical routes (keeps naming consistency and discoverability)
    Route::apiResource('posts', PostsController::class)->only(['index', 'store']);

    // Legacy singular endpoints kept temporarily for backward compatibility.
    // TODO: Remove after clients migrate to /posts
    Route::post('/post', [PostsController::class, 'store'])->name('api.posts.store_legacy');
    Route::get('/post', [PostsController::class, 'index'])->name('api.posts.index_legacy');
});
