<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PageController;
use App\Http\Controllers\Api\AuthController;

// Root page (if any browser-based UI is needed). For a mobile-first backend this
// may be minimal but is safe to keep.
Route::get('/', [PageController::class, 'home']);

// App-to-Middleware Authentication Routes (initiated by the mobile app).
// These routes participate in a browser redirect flow to the IdP (Okta).
// They live in web.php because they use session for state/PKCE storage and
// perform redirects.
Route::prefix('auth')->group(function () {
    // Initiates the Okta authorization redirect. Throttle to mitigate abuse.
    Route::get('/login', [AuthController::class, 'login'])
        ->middleware('throttle:30,1')
        ->name('auth.login');
    Route::get('/success', [AuthController::class, 'success'])->name('auth.success');
    Route::get('/error', [AuthController::class, 'error'])->name('auth.error');
    // Exchange short-lived deep link JWT for an API token. This endpoint is
    // called by the mobile app and is stateless; CSRF is disabled intentionally.
    // Add tight throttling as it is auth-sensitive.
    Route::post('/exchange-token', [AuthController::class, 'exchangeToken'])
        ->withoutMiddleware([\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class])
        ->middleware('throttle:10,1')
        ->name('auth.exchange-token');
});

// Middleware-to-SSO Provider Routes (Okta callbacks)
// The IdP (Okta) redirects the user's browser back to this callback after login.
// It validates state/PKCE, exchanges code for tokens, and deep-links back to the app.
Route::prefix('sso-auth')->group(function () {
    Route::get('/callback', [AuthController::class, 'callback'])
        ->middleware('throttle:30,1')
        ->name('sso-auth.callback');
});

