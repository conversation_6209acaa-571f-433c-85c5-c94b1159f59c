<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Testing\RefreshDatabaseState;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait RefreshInMemoryDatabase
{
    /**
     * Define hooks to migrate the database before and after each test.
     */
    public function refreshInMemoryDatabase(): void
    {
        // Force SQLite in-memory for all tests regardless of container env
        config([
            'database.default' => 'sqlite',
            'database.connections.sqlite.database' => ':memory:',
        ]);

        // Ensure the connection picks up the new in-memory config
        DB::purge('sqlite');
        DB::reconnect('sqlite');
        DB::setDefaultConnection('sqlite');

        try {
            $this->artisan('db:wipe', [
                '--database' => 'sqlite',
                '--drop-views' => true,
            ]);
        } catch (\Throwable $e) {
            // On some environments (e.g., SQLite file permissions), db:wipe may fail.
            // We proceed with manual table recreation to ensure a clean state for tests.
        }

        $this->createTables();

        $this->app[Kernel::class]->setArtisan(null);
    }

    /**
     * Begin a database transaction on the testing database.
     */
    public function beginDatabaseTransaction(): void
    {
        $database = $this->app->make('db');

        foreach ($this->connectionsToTransact() as $name) {
            $connection = $database->connection($name);
            $dispatcher = $connection->getEventDispatcher();

            $connection->unsetEventDispatcher();
            $connection->beginTransaction();
            $connection->setEventDispatcher($dispatcher);
        }

        $this->beforeApplicationDestroyed(function () use ($database) {
            foreach ($this->connectionsToTransact() as $name) {
                $connection = $database->connection($name);
                $dispatcher = $connection->getEventDispatcher();

                $connection->unsetEventDispatcher();
                $connection->rollback();
                $connection->setEventDispatcher($dispatcher);
                $connection->disconnect();
            }
        });
    }

    /**
     * The database connections that should have transactions.
     */
    protected function connectionsToTransact(): array
    {
        return property_exists($this, 'connectionsToTransact')
                            ? $this->connectionsToTransact : [null];
    }

    /**
     * Create the database tables for testing.
     */
    protected function createTables(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('user_sessions');
        Schema::dropIfExists('users');

        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('okta_user_id')->nullable()->unique();
            $table->string('okta_email')->nullable();
            // Use text for broad SQLite compatibility during tests
            $table->text('okta_profile_data')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('user_sessions', function ($table) {
            $table->id();
            // Avoid FK constraints in SQLite to prevent grammar issues
            $table->unsignedBigInteger('user_id');
            $table->text('okta_access_token')->nullable();
            $table->text('okta_refresh_token')->nullable();
            $table->text('okta_id_token')->nullable();
            $table->timestamp('okta_expires_at')->nullable();
            $table->string('app_token_hash')->nullable();
            $table->string('platform')->default('mobile');
            $table->string('okta_session_id')->nullable();
            // Use text for broad SQLite compatibility during tests
            $table->text('okta_user_data')->nullable();
            $table->string('state')->nullable();
            $table->string('code_verifier')->nullable();
            $table->string('code_challenge')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_activity_at')->nullable();
            $table->timestamps();
        });

        Schema::create('personal_access_tokens', function ($table) {
            $table->id();
            $table->morphs('tokenable');
            $table->string('name');
            $table->string('token', 64)->unique();
            $table->text('abilities')->nullable();
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }
}
