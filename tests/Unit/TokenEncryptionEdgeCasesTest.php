<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Tests\RefreshInMemoryDatabase;

class TokenEncryptionEdgeCasesTest extends TestCase
{
    use RefreshInMemoryDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->refreshInMemoryDatabase();

        $this->user = User::factory()->create();
    }

    public function test_token_encryption_with_null_values()
    {
        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => null,
            'okta_refresh_token' => null,
            'okta_token_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should handle null values gracefully
        $this->assertNull($userSession->okta_access_token);
        $this->assertNull($userSession->okta_refresh_token);
        
        // Refresh from database to ensure it was stored correctly
        $userSession->refresh();
        $this->assertNull($userSession->okta_access_token);
        $this->assertNull($userSession->okta_refresh_token);
    }

    public function test_token_encryption_with_empty_strings()
    {
        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => '',
            'okta_refresh_token' => '',
            'okta_token_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should handle empty strings
        $this->assertEquals('', $userSession->okta_access_token);
        $this->assertEquals('', $userSession->okta_refresh_token);
        
        // Refresh from database to ensure it was stored correctly
        $userSession->refresh();
        $this->assertEquals('', $userSession->okta_access_token);
        $this->assertEquals('', $userSession->okta_refresh_token);
    }

    public function test_token_encryption_with_very_long_tokens()
    {
        // Create very long tokens (simulate real-world JWT tokens)
        $longAccessToken = str_repeat('a', 2000) . '.very.long.jwt.token.with.lots.of.data';
        $longRefreshToken = str_repeat('b', 1500) . '.another.very.long.refresh.token';

        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => $longAccessToken,
            'okta_refresh_token' => $longRefreshToken,
            'okta_token_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should handle very long tokens
        $this->assertEquals($longAccessToken, $userSession->okta_access_token);
        $this->assertEquals($longRefreshToken, $userSession->okta_refresh_token);
        
        // Refresh from database to ensure it was stored and decrypted correctly
        $userSession->refresh();
        $this->assertEquals($longAccessToken, $userSession->okta_access_token);
        $this->assertEquals($longRefreshToken, $userSession->okta_refresh_token);
    }

    public function test_token_encryption_with_special_characters()
    {
        // Create tokens with special characters
        $specialAccessToken = 'access.token.with.special.chars!@#$%^&*()_+-={}[]|\\:";\'<>?,./';
        $specialRefreshToken = 'refresh.token.with.unicode.chars.αβγδε.中文.🚀🎉';

        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => $specialAccessToken,
            'okta_refresh_token' => $specialRefreshToken,
            'okta_token_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should handle special characters
        $this->assertEquals($specialAccessToken, $userSession->okta_access_token);
        $this->assertEquals($specialRefreshToken, $userSession->okta_refresh_token);
        
        // Refresh from database to ensure it was stored and decrypted correctly
        $userSession->refresh();
        $this->assertEquals($specialAccessToken, $userSession->okta_access_token);
        $this->assertEquals($specialRefreshToken, $userSession->okta_refresh_token);
    }

    public function test_encryption_decryption_error_handling()
    {
        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'valid-access-token',
            'okta_refresh_token' => 'valid-refresh-token',
            'okta_token_expires_at' => now()->addHour(),
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Manually corrupt the encrypted data in the database to test error handling
        $userSession->getConnection()->table('user_sessions')
            ->where('id', $userSession->id)
            ->update([
                'okta_access_token' => 'corrupted-encrypted-data',
                'okta_refresh_token' => 'another-corrupted-encrypted-data',
            ]);

        // Refresh the model to get the corrupted data
        $userSession = UserSession::find($userSession->id);

        // The model should handle decryption errors gracefully
        // This might throw an exception or return null/empty, depending on implementation
        try {
            $accessToken = $userSession->okta_access_token;
            $refreshToken = $userSession->okta_refresh_token;

            // If no exception is thrown, the values should be handled gracefully
            $this->assertTrue(true, 'Decryption error handled gracefully');
        } catch (\Exception $e) {
            // If an exception is thrown, it should be an encryption/decryption-related exception
            // The message might contain "payload", "invalid", "decrypt", etc.
            $message = strtolower($e->getMessage());
            $this->assertTrue(
                str_contains($message, 'decrypt') ||
                str_contains($message, 'payload') ||
                str_contains($message, 'invalid') ||
                str_contains($message, 'encrypt'),
                'Exception should be related to encryption/decryption: ' . $e->getMessage()
            );
        }
    }

    public function test_okta_user_data_encryption_with_complex_json()
    {
        $complexOktaData = [
            'sub' => 'user-id-123',
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'groups' => ['admin', 'users', 'developers'],
            'custom_claims' => [
                'department' => 'Engineering',
                'location' => 'San Francisco',
                'permissions' => [
                    'read' => true,
                    'write' => true,
                    'admin' => false
                ]
            ],
            'nested_data' => [
                'level1' => [
                    'level2' => [
                        'level3' => 'deep value'
                    ]
                ]
            ]
        ];

        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'access-token',
            'okta_refresh_token' => 'refresh-token',
            'okta_token_expires_at' => now()->addHour(),
            'okta_user_data' => $complexOktaData,
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should handle complex JSON data
        $this->assertEquals($complexOktaData, $userSession->okta_user_data);
        
        // Refresh from database to ensure it was stored and retrieved correctly
        $userSession->refresh();
        $this->assertEquals($complexOktaData, $userSession->okta_user_data);
        
        // Test specific nested values
        $this->assertEquals('Engineering', $userSession->okta_user_data['custom_claims']['department']);
        $this->assertEquals('deep value', $userSession->okta_user_data['nested_data']['level1']['level2']['level3']);
        $this->assertTrue($userSession->okta_user_data['custom_claims']['permissions']['read']);
    }

    public function test_okta_user_data_with_null_and_empty_values()
    {
        $dataWithNulls = [
            'sub' => 'user-id-123',
            'email' => null,
            'name' => '',
            'groups' => [],
            'optional_field' => null,
            'empty_object' => [],
            'zero_value' => 0,
            'false_value' => false,
        ];

        $userSession = UserSession::create([
            'user_id' => $this->user->id,
            'platform' => 'mobile',
            'okta_access_token' => 'access-token',
            'okta_refresh_token' => 'refresh-token',
            'okta_token_expires_at' => now()->addHour(),
            'okta_user_data' => $dataWithNulls,
            'app_token_hash' => hash('sha256', 'test-token'),
            'is_active' => true,
        ]);

        // Should preserve null and empty values correctly
        $retrievedData = $userSession->okta_user_data;
        
        $this->assertEquals('user-id-123', $retrievedData['sub']);
        $this->assertNull($retrievedData['email']);
        $this->assertEquals('', $retrievedData['name']);
        $this->assertEquals([], $retrievedData['groups']);
        $this->assertNull($retrievedData['optional_field']);
        $this->assertEquals([], $retrievedData['empty_object']);
        $this->assertEquals(0, $retrievedData['zero_value']);
        $this->assertFalse($retrievedData['false_value']);
    }

    public function test_concurrent_encryption_operations()
    {
        // Test multiple sessions being created simultaneously
        $sessions = [];
        
        for ($i = 0; $i < 5; $i++) {
            $sessions[] = UserSession::create([
                'user_id' => $this->user->id,
                'platform' => 'mobile',
                'okta_access_token' => "access-token-{$i}",
                'okta_refresh_token' => "refresh-token-{$i}",
                'okta_token_expires_at' => now()->addHour(),
                'okta_user_data' => ['session_id' => $i, 'data' => "session-data-{$i}"],
                'app_token_hash' => hash('sha256', "test-token-{$i}"),
                'is_active' => true,
            ]);
        }

        // Verify all sessions were created and encrypted correctly
        foreach ($sessions as $i => $session) {
            $this->assertEquals("access-token-{$i}", $session->okta_access_token);
            $this->assertEquals("refresh-token-{$i}", $session->okta_refresh_token);
            $this->assertEquals($i, $session->okta_user_data['session_id']);
            $this->assertEquals("session-data-{$i}", $session->okta_user_data['data']);
        }

        // Refresh all sessions from database and verify again
        foreach ($sessions as $i => $session) {
            $session->refresh();
            $this->assertEquals("access-token-{$i}", $session->okta_access_token);
            $this->assertEquals("refresh-token-{$i}", $session->okta_refresh_token);
            $this->assertEquals($i, $session->okta_user_data['session_id']);
            $this->assertEquals("session-data-{$i}", $session->okta_user_data['data']);
        }
    }
}
